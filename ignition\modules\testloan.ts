// ignition/modules/testloan.ts
import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";

export default buildModule("EmptyFlashLoanModule", (m) => {
    const vault = "0xBA12222222228d8Ba445958a75a0704d566BF2C8";
    const token = "0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174";

    const testloan = m.contract("testloan123", [vault, token]);

    //m.call(testloan, "flash", [100_000n]);

    return { testloan };
});
