// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

// 使用项目根目录的 OpenZeppelin 合约，避免版本冲突
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@uniswap/v3-core/contracts/interfaces/IUniswapV3Pool.sol";
import "@uniswap/v3-core/contracts/interfaces/callback/IUniswapV3SwapCallback.sol";

// 手动定义 TransferHelper 功能，避免版本冲突
library TransferHelper {
    function safeApprove(address token, address to, uint256 value) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.approve.selector, to, value)
        );
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper: APPROVE_FAILED"
        );
    }

    function safeTransfer(address token, address to, uint256 value) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.transfer.selector, to, value)
        );
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper: TRANSFER_FAILED"
        );
    }

    function safeTransferFrom(
        address token,
        address from,
        address to,
        uint256 value
    ) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(
                IERC20.transferFrom.selector,
                from,
                to,
                value
            )
        );
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper: TRANSFER_FROM_FAILED"
        );
    }
}

interface IVault {
    function flashLoan(
        address recipient,
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        bytes calldata userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        uint256[] calldata feeAmounts,
        bytes calldata userData
    ) external;
}

contract trueloan is IFlashLoanRecipient, IUniswapV3SwapCallback {
    using SafeERC20 for IERC20;

    // Balancer Vault（用于闪电贷）
    IVault public immutable vault;

    // 合约拥有者
    address public owner;

    // Uniswap V3 sqrtPrice 极限（TickMath 边界，常用作“无滑点限制”的默认）
    uint160 internal constant MIN_SQRT_RATIO = 4295128739 + 1; // MIN+1
    uint160 internal constant MAX_SQRT_RATIO =
        1461446703485210103287273052203988822378723970342 - 1; // MAX-1

    // 事件：记录套利盈利
    event ArbitrageProfit(address indexed token, uint256 profit);

    // 套利参数结构体（减少局部变量，避免 stack-too-deep）
    struct ArbParams {
        address tokenA;
        address tokenB;
        address pool1;
        address pool2;
        uint160 sqrt1;
        uint160 sqrt2;
        uint256 minOut1;
        uint256 minOut2;
        bool orderPool1First;
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner");
        _;
    }

    constructor(address _vault) {
        vault = IVault(_vault);
        owner = msg.sender;
    }

    // 触发闪电贷，并将跨池套利所需参数通过 userData 传入回调
    // 参数说明：
    // - flashToken: 闪电贷借入币种（要求与 tokenA 相同）
    // - amount: 借入数量
    // - tokenA, tokenB: 套利的两个币种（原 USDC/POL 改为动态传入）
    // - pool1, pool2: 直接指定要交互的两个 UniswapV3 池地址
    // - sqrt1, sqrt2: 两次 swap 的 sqrtPriceLimitX96（用于限制滑点，建议使用合适边界）
    // - minOut1, minOut2: 两次 swap 的最小可接受输出
    // - orderPool1First: true 表示先用 pool1 再用 pool2；false 反之
    function flash(
        address flashToken,
        uint256 amount,
        address tokenA,
        address tokenB,
        address pool1,
        address pool2,
        uint160 sqrt1,
        uint160 sqrt2,
        uint256 minOut1,
        uint256 minOut2,
        bool orderPool1First
    ) external onlyOwner {
        require(flashToken == tokenA, "flashToken must equal tokenA");

        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = IERC20(flashToken);

        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;

        ArbParams memory p = ArbParams({
            tokenA: tokenA,
            tokenB: tokenB,
            pool1: pool1,
            pool2: pool2,
            sqrt1: sqrt1,
            sqrt2: sqrt2,
            minOut1: minOut1,
            minOut2: minOut2,
            orderPool1First: orderPool1First
        });

        bytes memory userData = abi.encode(p);

        vault.flashLoan(address(this), tokens, amounts, userData);
    }

    // ----------------------------------
    // Balancer 闪电贷回调
    // ----------------------------------
    function receiveFlashLoan(
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        uint256[] calldata feeAmounts,
        bytes calldata userData
    ) external override(IFlashLoanRecipient) {
        require(msg.sender == address(vault), "!vault");
        require(tokens.length == 1, "tokens.length");

        // 将解码与路由逻辑放入内部函数，避免本函数局部变量过多导致 stack-too-deep
        _performArbitrage(tokens[0], amounts[0], userData);

        _repayFlashLoan(tokens[0], amounts[0] + feeAmounts[0]);
    }

    function _performArbitrage(
        IERC20 flashToken,
        uint256 amountIn,
        bytes calldata userData
    ) internal {
        ArbParams memory p = abi.decode(userData, (ArbParams));
        require(address(flashToken) == p.tokenA, "flash token != tokenA");

        address firstPool = p.orderPool1First ? p.pool1 : p.pool2;
        address secondPool = p.orderPool1First ? p.pool2 : p.pool1;

        bool zeroForOne = p.tokenA < p.tokenB; // A->B

        uint256 amountB = _swapExactInput(
            firstPool,
            zeroForOne,
            int256(amountIn),
            p.sqrt1,
            p.tokenA,
            p.tokenB
        );
        require(amountB >= p.minOut1, "minOut1");

        uint256 amountAback = _swapExactInput(
            secondPool,
            !zeroForOne,
            int256(amountB),
            p.sqrt2,
            p.tokenB,
            p.tokenA
        );
        require(amountAback >= p.minOut2, "minOut2");
    }

    function _swapExactInput(
        address pool,
        bool zeroForOne,
        int256 amountSpecified,
        uint160 sqrtLimit,
        address tokenIn,
        address tokenOut
    ) internal returns (uint256 amountReceived) {
        uint160 sqrtPriceLimitX96 = sqrtLimit == 0
            ? (zeroForOne ? MIN_SQRT_RATIO : MAX_SQRT_RATIO)
            : sqrtLimit;

        (int256 amount0Delta, int256 amount1Delta) = IUniswapV3Pool(pool).swap(
            address(this),
            zeroForOne,
            amountSpecified,
            sqrtPriceLimitX96,
            abi.encode(tokenIn, tokenOut)
        );

        amountReceived = uint256(zeroForOne ? -amount1Delta : -amount0Delta);
    }

    function _repayFlashLoan(IERC20 flashToken, uint256 repayAmount) internal {
        uint256 balance = flashToken.balanceOf(address(this));
        if (balance < repayAmount) {
            revert(
                string(
                    abi.encodePacked(
                        "Insufficient balance to repay flash loan. ",
                        "Required: ",
                        uintToStr(repayAmount),
                        ", Available: ",
                        uintToStr(balance)
                    )
                )
            );
        }

        uint256 profit = balance - repayAmount;

        // 先归还闪电贷
        flashToken.safeTransfer(address(vault), repayAmount);

        // 若有盈利则转给 owner 并记录事件

        if (profit > 0) {
            emit ArbitrageProfit(address(flashToken), profit);
            flashToken.safeTransfer(owner, profit);
        }
    }

    // ----------------------------------
    // Uniswap V3 Swap 回调：把应付款 token 转给池子
    // data 编码为 (address tokenIn, address tokenOut)
    // ----------------------------------
    function uniswapV3SwapCallback(
        int256 amount0Delta,
        int256 amount1Delta,
        bytes calldata data
    ) external override(IUniswapV3SwapCallback) {
        require(amount0Delta > 0 || amount1Delta > 0, "no debt");
        (address tokenIn, ) = abi.decode(data, (address, address));
        if (amount0Delta > 0) {
            TransferHelper.safeTransfer(
                tokenIn,
                msg.sender,
                uint256(amount0Delta)
            );
        } else if (amount1Delta > 0) {
            TransferHelper.safeTransfer(
                tokenIn,
                msg.sender,
                uint256(amount1Delta)
            );
        }
    }

    // ---- 工具：uint256 → string ----
    function uintToStr(uint256 value) internal pure returns (string memory) {
        if (value == 0) return "0";
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }
}
