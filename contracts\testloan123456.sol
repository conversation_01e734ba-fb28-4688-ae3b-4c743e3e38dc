// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

// 使用项目根目录的 OpenZeppelin 合约，避免版本冲突
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";

// 手动定义 TransferHelper 功能，避免版本冲突
library TransferHelper {
    function safeApprove(address token, address to, uint256 value) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.approve.selector, to, value)
        );
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper: APPROVE_FAILED"
        );
    }

    function safeTransfer(address token, address to, uint256 value) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(IERC20.transfer.selector, to, value)
        );
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper: TRANSFER_FAILED"
        );
    }

    function safeTransferFrom(
        address token,
        address from,
        address to,
        uint256 value
    ) internal {
        (bool success, bytes memory data) = token.call(
            abi.encodeWithSelector(
                IERC20.transferFrom.selector,
                from,
                to,
                value
            )
        );
        require(
            success && (data.length == 0 || abi.decode(data, (bool))),
            "TransferHelper: TRANSFER_FROM_FAILED"
        );
    }
}

interface IVault {
    function flashLoan(
        address recipient,
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        bytes calldata userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        uint256[] calldata feeAmounts,
        bytes calldata userData
    ) external;
}

contract testloan123456 is IFlashLoanRecipient {
    using SafeERC20 for IERC20;

    IVault public immutable vault;
    IERC20 public immutable token; // USDC

    // --- Uniswap V3 相关常量 ---
    ISwapRouter public constant SWAP_ROUTER =
        ISwapRouter(0xE592427A0AEce92De3Edee1F18E0157C05861564);
    address public constant USDC = 0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174;
    address public constant POL = 0x1BFD67037B42Cf73acF2047067bd4F2C47D9BfD6;
    uint24 public constant POOL_FEE = 500;

    constructor(address _vault, address _token) {
        vault = IVault(_vault);
        token = IERC20(_token);
    }

    // 任何人都能触发一次“借完就还”的闪电贷
    function flash(uint256 amount) external {
        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = token;

        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;

        vault.flashLoan(address(this), tokens, amounts, "");
    }

    // ----------------------------------
    // 闪电贷回调
    // ----------------------------------
    function receiveFlashLoan(
        IERC20[] calldata tokens,
        uint256[] calldata amounts,
        uint256[] calldata feeAmounts,
        bytes calldata
    ) external override {
        require(msg.sender == address(vault), "!vault");
        require(tokens.length == 1 && tokens[0] == token, "Token mismatch");

        _performArbitrage(amounts[0]);
        _repayFlashLoan(tokens[0], amounts[0] + feeAmounts[0]);
    }

    function _performArbitrage(uint256 loanAmount) internal {
        // ---- 1. 把 USDC 换成 POL ----
        TransferHelper.safeApprove(USDC, address(SWAP_ROUTER), loanAmount);

        uint256 polAmount = SWAP_ROUTER.exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: USDC,
                tokenOut: POL,
                fee: POOL_FEE,
                recipient: address(this),
                deadline: block.timestamp,
                amountIn: loanAmount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            })
        );

        // ---- 2. 把 POL 换回 USDC ----
        TransferHelper.safeApprove(POL, address(SWAP_ROUTER), polAmount);

        SWAP_ROUTER.exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: POL,
                tokenOut: USDC,
                fee: POOL_FEE,
                recipient: address(this),
                deadline: block.timestamp,
                amountIn: polAmount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            })
        );
    }

    function _repayFlashLoan(IERC20 flashToken, uint256 repayAmount) internal {
        uint256 balance = flashToken.balanceOf(address(this));

        if (balance < repayAmount) {
            revert(
                string(
                    abi.encodePacked(
                        "Insufficient balance to repay flash loan. ",
                        "Required: ",
                        uintToStr(repayAmount),
                        ", Available: ",
                        uintToStr(balance)
                    )
                )
            );
        }

        flashToken.safeTransfer(address(vault), repayAmount);
    }

    // ---- 工具：uint256 → string ----
    function uintToStr(uint256 value) internal pure returns (string memory) {
        if (value == 0) return "0";
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }
}
