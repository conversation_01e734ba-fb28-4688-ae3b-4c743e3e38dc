import { ethers } from "ethers";
import dotenv from "dotenv";
dotenv.config();

// ========= 1) 环境与常量 =========
const WS_URL =
    process.env.WS_URL ||
    "wss://api.zan.top/node/ws/v1/polygon/mainnet/********************************";
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const ARB_CONTRACT = process.env.ARB_CONTRACT; // trueloan 合约地址
const FLASH_AMOUNT = process.env.FLASH_AMOUNT || "1000"; // 人类可读数量，例如 1000（按 tokenA 的 decimals 解析）
const THRESHOLD_PCT = parseFloat(process.env.THRESHOLD_PCT || "0.15"); // 触发阈值（百分比值，例如 0.15 表示 0.15%）
const SLIPPAGE_BPS = parseInt(process.env.SLIPPAGE_BPS || "30", 10); // 30 = 0.30%
const COOLDOWN_MS = parseInt(process.env.COOLDOWN_MS || "60000", 10);

// 两个要监控并参与套利的池
const POOL1_ADDR =
    process.env.POOL1_ADDR || "******************************************";
const POOL2_ADDR =
    process.env.POOL2_ADDR || "******************************************";

if (!PRIVATE_KEY) throw new Error("Missing PRIVATE_KEY in .env");
if (!ARB_CONTRACT)
    throw new Error("Missing ARB_CONTRACT (trueloan address) in .env");

const provider = new ethers.WebSocketProvider(WS_URL);
const wallet = new ethers.Wallet(PRIVATE_KEY, provider);

// ========= 2) ABI =========
const ERC20_ABI = [
    "function symbol() view returns (string)",
    "function decimals() view returns (uint8)",
];

const POOL_ABI = [
    "event Swap(address indexed sender,address indexed recipient,int256 amount0,int256 amount1,uint160 sqrtPriceX96,uint128 liquidity,int24 tick)",
    "function slot0() external view returns (uint160 sqrtPriceX96,int24 tick,uint16,uint16,uint16,uint8,bool)",
    "function token0() external view returns (address)",
    "function token1() external view returns (address)",
];

const ARB_ABI = [
    "function flash(address flashToken,uint256 amount,address tokenA,address tokenB,address pool1,address pool2,uint160 sqrt1,uint160 sqrt2,uint256 minOut1,uint256 minOut2,bool orderPool1First)",
];

const arb = new ethers.Contract(ARB_CONTRACT, ARB_ABI, wallet);

// ========= 3) 工具 =========
const pool1 = new ethers.Contract(POOL1_ADDR, POOL_ABI, provider);
const pool2 = new ethers.Contract(POOL2_ADDR, POOL_ABI, provider);

const metaCache = new Map(); // tokenAddr -> {symbol, decimals}
async function getTokenMeta(addr) {
    if (metaCache.has(addr)) return metaCache.get(addr);
    const c = new ethers.Contract(addr, ERC20_ABI, provider);
    const [symbol, decimals] = await Promise.all([c.symbol(), c.decimals()]);
    const meta = { symbol, decimals: Number(decimals) };
    metaCache.set(addr, meta);
    return meta;
}

async function loadPoolMeta(pool) {
    const [addr0, addr1] = await Promise.all([pool.token0(), pool.token1()]);
    const [meta0, meta1] = await Promise.all([
        getTokenMeta(addr0),
        getTokenMeta(addr1),
    ]);
    return { addr0, addr1, meta0, meta1 }; // token0 -> meta0, token1 -> meta1
}

function priceStrFromSqrt(sqrtPriceX96, dec0, dec1) {
    // price = (sqrtPriceX96^2 / 2^192) * 10^(dec0 - dec1)
    const Q96 = 2n ** 96n;
    const n = BigInt(sqrtPriceX96) * BigInt(sqrtPriceX96);
    let num = n;
    let den = Q96 * Q96;
    const delta = BigInt(dec0) - BigInt(dec1);
    if (delta >= 0n) {
        num = num * 10n ** delta;
    } else {
        den = den * 10n ** -delta;
    }
    const SCALE = 1_000_000_000_000_000_000n; // 1e18
    const priceScaled = (num * SCALE) / den; // price * 1e18
    return ethers.formatUnits(priceScaled, 18);
}

function logSwap(poolName, amount0, amount1, sqrtPriceX96, meta0, meta1) {
    const isToken0In = amount0 > 0n;
    const tokenInMeta = isToken0In ? meta0 : meta1;
    const tokenOutMeta = isToken0In ? meta1 : meta0;
    const amountIn = ethers.formatUnits(
        isToken0In ? amount0 : amount1,
        tokenInMeta.decimals
    );
    const amountOut = ethers.formatUnits(
        isToken0In ? -amount1 : -amount0,
        tokenOutMeta.decimals
    );
    const priceStr = priceStrFromSqrt(
        sqrtPriceX96,
        meta0.decimals,
        meta1.decimals
    );
    const now = new Date().toISOString();
    console.log(
        `[${now}] ${poolName} Swap | ${tokenInMeta.symbol}->${tokenOutMeta.symbol} | in=${amountIn} | out=${amountOut} | px=${priceStr} ${tokenOutMeta.symbol}/${tokenInMeta.symbol}`
    );
}

// 计算 tokenA -> tokenB 的最小接收量（以价格和精度换算）
function calcMinOut1(amountSmallest, priceStr, decIn, decOut, slippageBps) {
    const SCALE = 1_000_000_000_000n; // 1e12
    const pxScaled = BigInt(Math.floor(parseFloat(priceStr) * 1e12));
    const num =
        amountSmallest *
        pxScaled *
        10n ** BigInt(decOut) *
        BigInt(10000 - slippageBps);
    const den = 10n ** BigInt(decIn) * 10000n * SCALE;
    return num / den; // tokenOut
}

// 计算 tokenB -> tokenA 的最小接收量（以价格和精度换算）
function calcMinOut2(amountB, priceStr, decB, decA, slippageBps) {
    const SCALE = 1_000_000_000_000n; // 1e12
    const pxScaled = BigInt(Math.floor(parseFloat(priceStr) * 1e12)); // token1/token0
    const num =
        amountB * 10n ** BigInt(decA) * BigInt(10000 - slippageBps) * SCALE;
    const den = pxScaled * 10n ** BigInt(decB) * 10000n;
    return num / den; // tokenA
}

// 基于当前 sqrtPriceX96 和期望滑点的 sqrtPriceLimit 近似（避免引 TickMath 依赖）
function approxSqrtLimit(currentSqrt, zeroForOne, slippageBps) {
    const s = slippageBps / 10000; // 比例
    const f = Math.sqrt(zeroForOne ? 1 - s : 1 + s); // sqrt(1±s)
    const SCALE = 100_000_000n; // 1e8
    const fScaled = BigInt(Math.round(f * Number(SCALE)));
    return (currentSqrt * fScaled) / SCALE;
}

// ========= 4) 主程序 =========
(async () => {
    const [m1, m2] = await Promise.all([
        loadPoolMeta(pool1),
        loadPoolMeta(pool2),
    ]);
    console.log(
        "Pool1:",
        m1.meta0.symbol,
        m1.meta0.decimals,
        "|",
        m1.meta1.symbol,
        m1.meta1.decimals
    );
    console.log(
        "Pool2:",
        m2.meta0.symbol,
        m2.meta0.decimals,
        "|",
        m2.meta1.symbol,
        m2.meta1.decimals
    );

    // 我们选用 pool1 的 token0/1 作为合约 flash 的 tokenA/tokenB（你可按需更改）
    const tokenA = m1.addr0; // 借入 token = token0
    const tokenB = m1.addr1; // 对手 token = token1
    const decA = m1.meta0.decimals;

    let latestPrice1 = null; // token1/token0
    let latestPrice2 = null; // token1/token0
    let lastFiredAt = 0;
    let sending = false; // 并发保护，避免 nonce 冲突

    const tryFire = async () => {
        if (!latestPrice1 || !latestPrice2) return;
        const now = Date.now();
        if (sending) return;
        if (now - lastFiredAt < COOLDOWN_MS) return;

        const p1 = parseFloat(latestPrice1);
        const p2 = parseFloat(latestPrice2);
        if (!isFinite(p1) || !isFinite(p2) || p1 <= 0 || p2 <= 0) return;

        const diff = ((p1 - p2) / p2) * 100; // 百分比
        if (!isFinite(diff)) return;
        const absDiff = Math.abs(diff);

        const ts = new Date().toISOString();
        console.log(
            `[${ts}] p1=${latestPrice1} | p2=${latestPrice2} | diff=${diff.toFixed(
                3
            )}%`
        );

        if (absDiff <= THRESHOLD_PCT) return;

        // 动态决定下单顺序：低价池买（A->B），高价池卖（B->A）
        const orderPool1First = p1 < p2; // pool1 价格更低 => 先用 pool1
        const firstPool = orderPool1First ? pool1 : pool2;
        const secondPool = orderPool1First ? pool2 : pool1;
        const firstPriceStr = orderPool1First ? latestPrice1 : latestPrice2;
        const secondPriceStr = orderPool1First ? latestPrice2 : latestPrice1;

        // 计算借入数量与最小产出
        const amount = ethers.parseUnits(FLASH_AMOUNT, decA); // tokenA 的最小单位
        //const minOut1 = 0n;
        //const minOut2 = 0n;
        const minOut1 = calcMinOut1(
            amount,
            firstPriceStr,
            m1.meta0.decimals,
            m1.meta1.decimals,
            SLIPPAGE_BPS
        ); // tokenB
        const minOut2 = calcMinOut2(
            minOut1,
            secondPriceStr,
            m1.meta1.decimals,
            m1.meta0.decimals,
            SLIPPAGE_BPS
        ); // tokenA

        // 计算 sqrtPriceLimitX96（基于当前 slot0 sqrt & 允许滑点）
        const [s0First, s0Second] = await Promise.all([
            firstPool.slot0(),
            secondPool.slot0(),
        ]);
        const sqrtFirst = s0First.sqrtPriceX96 ?? s0First[0];
        const sqrtSecond = s0Second.sqrtPriceX96 ?? s0Second[0];
        const zeroForOneFirst = tokenA.toLowerCase() < tokenB.toLowerCase(); // A->B
        const zeroForOneSecond = !zeroForOneFirst; // B->A
        const sqrt1 = approxSqrtLimit(sqrtFirst, zeroForOneFirst, SLIPPAGE_BPS);
        const sqrt2 = approxSqrtLimit(
            sqrtSecond,
            zeroForOneSecond,
            SLIPPAGE_BPS
        );

        console.log(
            `[Fire] diff=${absDiff.toFixed(
                3
            )}% => flash amount=${FLASH_AMOUNT} | orderPool1First=${orderPool1First}`
        );
        console.log(
            `sqrt1=${sqrt1.toString()} | sqrt2=${sqrt2.toString()} | minOut1=${minOut1.toString()} | minOut2=${minOut2.toString()}`
        );
        sending = true;
        try {
            const tx = await arb.flash(
                tokenA,
                amount,
                tokenA,
                tokenB,
                orderPool1First ? POOL1_ADDR : POOL2_ADDR,
                orderPool1First ? POOL2_ADDR : POOL1_ADDR,
                sqrt1,
                sqrt2,
                minOut1,
                minOut2,
                orderPool1First,
                { gasLimit: 2_000_000 }
            );
            console.log("flash sent:", tx.hash);
            const rcpt = await tx.wait();
            console.log("flash mined in block", rcpt.blockNumber);
            lastFiredAt = Date.now();
        } catch (e) {
            console.error("flash failed:", e);
        } finally {
            sending = false;
        }
    };

    pool1.on("Swap", (sender, recipient, amount0, amount1, sqrtPriceX96) => {
        latestPrice1 = priceStrFromSqrt(
            sqrtPriceX96,
            m1.meta0.decimals,
            m1.meta1.decimals
        );
        logSwap("Pool1", amount0, amount1, sqrtPriceX96, m1.meta0, m1.meta1);
        tryFire().catch(console.error);
    });

    pool2.on("Swap", (sender, recipient, amount0, amount1, sqrtPriceX96) => {
        latestPrice2 = priceStrFromSqrt(
            sqrtPriceX96,
            m2.meta0.decimals,
            m2.meta1.decimals
        );
        logSwap("Pool2", amount0, amount1, sqrtPriceX96, m2.meta0, m2.meta1);
        tryFire().catch(console.error);
    });
})();
