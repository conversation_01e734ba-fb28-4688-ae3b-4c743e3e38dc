{"_format": "hh3-artifact-1", "contractName": "trueloan", "sourceName": "contracts/trueloan.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_vault", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "profit", "type": "uint256"}], "name": "ArbitrageProfit", "type": "event"}, {"inputs": [{"internalType": "address", "name": "flashToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "tokenA", "type": "address"}, {"internalType": "address", "name": "tokenB", "type": "address"}, {"internalType": "address", "name": "pool1", "type": "address"}, {"internalType": "address", "name": "pool2", "type": "address"}, {"internalType": "uint160", "name": "sqrt1", "type": "uint160"}, {"internalType": "uint160", "name": "sqrt2", "type": "uint160"}, {"internalType": "uint256", "name": "minOut1", "type": "uint256"}, {"internalType": "uint256", "name": "minOut2", "type": "uint256"}, {"internalType": "bool", "name": "orderPool1First", "type": "bool"}], "name": "flash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IERC20[]", "name": "tokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "feeAmounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}], "name": "receiveFlashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "amount0Delta", "type": "int256"}, {"internalType": "int256", "name": "amount1Delta", "type": "int256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "uniswapV3SwapCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "vault", "outputs": [{"internalType": "contract IVault", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}, "immutableReferences": {"1286": [{"length": 32, "start": 199}, {"length": 32, "start": 728}, {"length": 32, "start": 860}, {"length": 32, "start": 1928}]}, "inputSourceName": "project/contracts/trueloan.sol", "buildInfoId": "solc-0_8_28-82eaadb2116251a8123f0786dc182914d3675ebe"}