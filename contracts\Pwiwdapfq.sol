// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {IVault} from "@balancer-labs/v2-interfaces/contracts/vault/IVault.sol";
import {IFlashLoanRecipient} from "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol";
import {ISwapRouter} from "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import {IERC20 as IERC20oz} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IERC20 as IERC20bal} from "@balancer-labs/v2-interfaces/contracts/solidity-utils/openzeppelin/IERC20.sol";

contract Pwiwdapfq is IFlashLoanRecipient {
    using SafeERC20 for IERC20oz;

    // ====== Constants (Polygon Mainnet) ======
    address public constant USDC = 0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174;
    address public constant WPOL = 0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270;

    // Balancer V2 Vault
    IVault public constant VAULT =
        IVault(0xBA12222222228d8Ba445958a75a0704d566BF2C8);

    // Uniswap v3 & QuickSwap v3 routers(sushiswap)
    ISwapRouter public constant UNI_V3 =
        ISwapRouter(0xE592427A0AEce92De3Edee1F18E0157C05861564);
    ISwapRouter public constant QUICK_V3 =
        ISwapRouter(0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506);

    uint24 private constant FEE_500 = 500;

    address public owner;

    // ====== Events ======
    event Executed(
        address indexed caller,
        uint256 amountUSDC,
        bool orderUniToQuick
    );
    event Swapped(
        address indexed router,
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 amountOut
    );
    event Profit(address indexed to, uint256 profitUSDC);
    event Withdraw(address indexed to, address token, uint256 amount);

    // ====== Constructor ======
    constructor() {
        owner = msg.sender;

        IERC20oz(USDC).forceApprove(address(UNI_V3), type(uint256).max);
        IERC20oz(USDC).forceApprove(address(QUICK_V3), type(uint256).max);
        IERC20oz(WPOL).forceApprove(address(UNI_V3), type(uint256).max);
        IERC20oz(WPOL).forceApprove(address(QUICK_V3), type(uint256).max);
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "not owner");
        _;
    }

    // ====== External entry ======
    function execute(
        uint256 amountUSDC,
        bool order,
        uint256 minOut1,
        uint256 minOut2,
        uint256 deadline
    ) external onlyOwner {
        require(amountUSDC > 0, "amount=0");
        require(deadline >= block.timestamp, "deadline expired");

        IERC20bal[] memory tokens = new IERC20bal[](1);
        tokens[0] = IERC20bal(USDC);
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amountUSDC;

        // 打包参数
        bytes memory userData = abi.encode(
            order,
            minOut1,
            minOut2,
            deadline,
            amountUSDC
        );
        emit Executed(msg.sender, amountUSDC, order);

        VAULT.flashLoan(this, tokens, amounts, userData);
    }

    // ====== Balancer Callback ======
    function receiveFlashLoan(
        IERC20bal[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external override {
        require(msg.sender == address(VAULT), "only Vault");

        (
            bool order,
            uint256 minOut1,
            uint256 minOut2,
            uint256 deadline,
            uint256 amountUSDC
        ) = abi.decode(userData, (bool, uint256, uint256, uint256, uint256));

        uint256 usdcBack;
        if (order) {
            uint256 wpolOut = _swapExactIn(
                UNI_V3,
                USDC,
                WPOL,
                amountUSDC,
                minOut1,
                deadline
            );
            emit Swapped(address(UNI_V3), USDC, WPOL, amountUSDC, wpolOut);

            usdcBack = _swapExactIn(
                QUICK_V3,
                WPOL,
                USDC,
                wpolOut,
                minOut2,
                deadline
            );
            emit Swapped(address(QUICK_V3), WPOL, USDC, wpolOut, usdcBack);
        } else {
            uint256 wpolOut = _swapExactIn(
                QUICK_V3,
                USDC,
                WPOL,
                amountUSDC,
                minOut1,
                deadline
            );
            emit Swapped(address(QUICK_V3), USDC, WPOL, amountUSDC, wpolOut);

            usdcBack = _swapExactIn(
                UNI_V3,
                WPOL,
                USDC,
                wpolOut,
                minOut2,
                deadline
            );
            emit Swapped(address(UNI_V3), WPOL, USDC, wpolOut, usdcBack);
        }

        uint256 repay = amounts[0] + feeAmounts[0];
        require(usdcBack >= repay, "no profit");

        // 归还闪电贷
        IERC20oz(address(tokens[0])).safeTransfer(address(VAULT), repay);

        // 利润转 owner
        uint256 profit = usdcBack - repay;
        if (profit > 0) {
            IERC20oz(address(tokens[0])).safeTransfer(owner, profit);
            emit Profit(owner, profit);
        }
    }

    // ====== Internal swap helper ======
    function _swapExactIn(
        ISwapRouter router,
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 amountOutMin,
        uint256 deadline
    ) internal returns (uint256 amountOut) {
        require(amountIn > 0, "amountIn=0");
        require(deadline >= block.timestamp, "deadline expired");

        ISwapRouter.ExactInputSingleParams memory p = ISwapRouter
            .ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: FEE_500,
                recipient: address(this),
                deadline: deadline,
                amountIn: amountIn,
                amountOutMinimum: amountOutMin,
                sqrtPriceLimitX96: 0
            });

        amountOut = router.exactInputSingle(p);
    }

    // ====== Owner withdrawals ======
    function withdraw(address token, uint256 amount) external onlyOwner {
        IERC20oz(token).safeTransfer(owner, amount);
        emit Withdraw(owner, token, amount);
    }
}
