{"devDependencies": {"@balancer-labs/v2-interfaces": "^0.4.0", "@nomicfoundation/hardhat-ignition": "^3.0.1", "@nomicfoundation/hardhat-toolbox-mocha-ethers": "^3.0.0", "@openzeppelin/contracts": "^5.4.0", "@types/chai": "^4.3.20", "@types/chai-as-promised": "^8.0.2", "@types/mocha": "^10.0.10", "@types/node": "^22.18.0", "@uniswap/v3-periphery": "^1.4.4", "chai": "^5.3.3", "ethers": "^6.15.0", "forge-std": "github:foundry-rs/forge-std#v1.9.4", "hardhat": "^3.0.3", "mocha": "^11.7.1", "typescript": "~5.8.0"}, "type": "module", "dependencies": {"dotenv": "^17.2.1"}}