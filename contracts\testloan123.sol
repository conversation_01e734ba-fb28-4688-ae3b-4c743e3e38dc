// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

interface IVault {
    function flashLoan(
        IFlashLoanRecipient recipient,
        IERC20[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

contract testloan123 is IFlashLoanRecipient {
    using SafeERC20 for IERC20;

    IVault public immutable vault;
    IERC20 public immutable token;

    constructor(address _vault, address _token) {
        vault = IVault(_vault);
        token = IERC20(_token);
    }

    // 任何人都可以触发一次“借完就还”的闪电贷
    function flash(uint256 amount) external {
        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = token;

        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;

        // 把 amount 打包进 userData
        bytes memory userData = abi.encode(amount);

        vault.flashLoan(this, tokens, amounts, userData);
    }

    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory // userData 不再用，直接留空
    ) external override {
        require(msg.sender == address(vault), "!vault");
        require(tokens.length == 1 && tokens[0] == token, "Token mismatch");

        //to do

        uint256 repay = amounts[0] + feeAmounts[0];
        tokens[0].safeTransfer(address(vault), repay);
    }
}
